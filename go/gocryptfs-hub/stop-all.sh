#!/bin/bash

# GoCryptFS Hub - Stop All Services
# This script stops the Hub Server, Backend API, and Frontend UI

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$PROJECT_ROOT/logs"

echo -e "${BLUE}🛑 Stopping GoCryptFS Hub System${NC}"
echo

# Function to stop a service by PID
stop_service() {
    local pid_file=$1
    local service_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${YELLOW}🛑 Stopping $service_name (PID: $pid)...${NC}"
            kill "$pid" 2>/dev/null || true
            
            # Wait for process to stop
            local attempts=0
            while ps -p "$pid" > /dev/null 2>&1 && [ $attempts -lt 10 ]; do
                sleep 1
                attempts=$((attempts + 1))
            done
            
            if ps -p "$pid" > /dev/null 2>&1; then
                echo -e "${RED}⚠️  Force killing $service_name...${NC}"
                kill -9 "$pid" 2>/dev/null || true
            fi
            
            echo -e "${GREEN}✅ $service_name stopped${NC}"
        else
            echo -e "${YELLOW}⚠️  $service_name was not running${NC}"
        fi
        rm -f "$pid_file"
    else
        echo -e "${YELLOW}⚠️  No PID file found for $service_name${NC}"
    fi
}

# Function to stop processes by port
stop_by_port() {
    local port=$1
    local service_name=$2
    
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    if [ -n "$pids" ]; then
        echo -e "${YELLOW}🛑 Stopping $service_name on port $port...${NC}"
        echo "$pids" | xargs kill 2>/dev/null || true
        sleep 2
        
        # Force kill if still running
        local remaining_pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            echo -e "${RED}⚠️  Force killing $service_name...${NC}"
            echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
        fi
        echo -e "${GREEN}✅ $service_name stopped${NC}"
    else
        echo -e "${YELLOW}⚠️  No process found on port $port for $service_name${NC}"
    fi
}

# Stop services using PID files if they exist
if [ -d "$LOG_DIR" ]; then
    echo -e "${BLUE}📋 Stopping services using PID files...${NC}"
    
    stop_service "$LOG_DIR/hub-server.pid" "Hub Server"
    stop_service "$LOG_DIR/backend.pid" "Backend API"
    stop_service "$LOG_DIR/frontend.pid" "Frontend UI"
    
    echo
fi

# Also stop by port as backup
echo -e "${BLUE}🔍 Checking for remaining processes on service ports...${NC}"

stop_by_port 8080 "Hub Server"
stop_by_port 8081 "Backend API" 
stop_by_port 3000 "Frontend UI"

# Stop any remaining Node.js processes that might be related
echo -e "${BLUE}🧹 Cleaning up any remaining Node.js processes...${NC}"

# Find and stop any npm/node processes in our project directories
pkill -f "npm start" 2>/dev/null || true
pkill -f "node.*server.js" 2>/dev/null || true
pkill -f "react-scripts start" 2>/dev/null || true

# Clean up log files if requested
if [ "$1" = "--clean-logs" ]; then
    echo -e "${BLUE}🧹 Cleaning up log files...${NC}"
    rm -rf "$LOG_DIR"
    echo -e "${GREEN}✅ Log files cleaned${NC}"
fi

echo
echo -e "${GREEN}🎉 All services stopped successfully!${NC}"
echo
echo -e "${BLUE}📋 Next steps:${NC}"
echo "  🚀 Start again: ./start-all.sh"
echo "  📊 View logs:   ls -la logs/ (if logs exist)"
echo "  🧹 Clean logs:  ./stop-all.sh --clean-logs"
echo
