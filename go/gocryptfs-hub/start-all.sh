#!/bin/bash

# GoCryptFS Hub - Start All Services
# This script starts the Hub Server, Backend API, and Frontend UI

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WEB_UI_DIR="$PROJECT_ROOT/web-ui"
BACKEND_DIR="$WEB_UI_DIR/backend"
HUB_DIR="$PROJECT_ROOT/hub"

echo -e "${BLUE}🚀 Starting GoCryptFS Hub System${NC}"
echo "Project root: $PROJECT_ROOT"
echo

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
port_available() {
    ! nc -z localhost "$1" 2>/dev/null
}

# Function to wait for service to be ready
# wait_for_service() {
#     local port=$1
#     local service_name=$2
#     local max_attempts=30
#     local attempt=1
    
#     echo -e "${YELLOW}⏳ Waiting for $service_name to start on port $port...${NC}"
    
#     while [ $attempt -le $max_attempts ]; do
#         if nc -z localhost "$port" 2>/dev/null; then
#             echo -e "${GREEN}✅ $service_name is ready on port $port${NC}"
#             return 0
#         fi
#         sleep 1
#         attempt=$((attempt + 1))
#     done
    
#     echo -e "${RED}❌ $service_name failed to start on port $port${NC}"
#     return 1
# }

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

if ! command_exists "go"; then
    echo -e "${RED}❌ Go is not installed${NC}"
    exit 1
fi

if ! command_exists "node"; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    exit 1
fi

if ! command_exists "npm"; then
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites are installed${NC}"
echo

# Check if ports are available
echo -e "${BLUE}🔍 Checking port availability...${NC}"

if ! port_available 8080; then
    echo -e "${RED}❌ Port 8080 (Hub Server) is already in use${NC}"
    exit 1
fi

if ! port_available 8081; then
    echo -e "${RED}❌ Port 8081 (Backend API) is already in use${NC}"
    exit 1
fi

if ! port_available 3000; then
    echo -e "${RED}❌ Port 3000 (Frontend UI) is already in use${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All ports are available${NC}"
echo

# Build Go CLI if needed
echo -e "${BLUE}🔨 Building Go CLI application...${NC}"
cd "$PROJECT_ROOT"

if [ ! -f "main" ] || [ "main.go" -nt "main" ]; then
    echo "Building main binary..."
    go build -o main .
    echo -e "${GREEN}✅ Go CLI built successfully${NC}"
else
    echo -e "${GREEN}✅ Go CLI binary is up to date${NC}"
fi
echo

# Install dependencies if needed
echo -e "${BLUE}📦 Checking dependencies...${NC}"

# Frontend dependencies
cd "$WEB_UI_DIR"
if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
    echo -e "${GREEN}✅ Frontend dependencies installed${NC}"
else
    echo -e "${GREEN}✅ Frontend dependencies are up to date${NC}"
fi

# Backend dependencies
cd "$BACKEND_DIR"
if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
    echo "Installing backend dependencies..."
    npm install
    echo -e "${GREEN}✅ Backend dependencies installed${NC}"
else
    echo -e "${GREEN}✅ Backend dependencies are up to date${NC}"
fi
echo

# Create log directory
LOG_DIR="$PROJECT_ROOT/logs"
mkdir -p "$LOG_DIR"

# Start services
echo -e "${BLUE}🚀 Starting services...${NC}"

# Start Hub Server (if it exists)
cd "$PROJECT_ROOT"
if [ -f "main" ]; then
    echo -e "${YELLOW}🌐 Starting Hub Server on port 8080...${NC}"
    cd "$HUB_DIR"
    nohup ./main > "$LOG_DIR/hub-server.log" 2>&1 &
    HUB_PID=$!
    echo $HUB_PID > "$LOG_DIR/hub-server.pid"
    echo "Hub Server PID: $HUB_PID"
    
    # Wait for hub server to start
    # wait_for_service 8080 "Hub Server"
else
    echo -e "${YELLOW}⚠️  Hub Server binary not found, skipping...${NC}"
fi

# Start Backend API
echo -e "${YELLOW}🔧 Starting Backend API on port 8081...${NC}"
cd "$BACKEND_DIR"
nohup npm start > "$LOG_DIR/backend.log" 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > "$LOG_DIR/backend.pid"
echo "Backend API PID: $BACKEND_PID"

# Wait for backend to start
# wait_for_service 8081 "Backend API"

# Start Frontend UI
echo -e "${YELLOW}🎨 Starting Frontend UI on port 3000...${NC}"
cd "$WEB_UI_DIR"
nohup npm start > "$LOG_DIR/frontend.log" 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"
echo "Frontend UI PID: $FRONTEND_PID"

# Wait for frontend to start
# wait_for_service 3000 "Frontend UI"

echo
echo -e "${GREEN}🎉 All services started successfully!${NC}"
echo
echo -e "${BLUE}📍 Access Points:${NC}"
echo "  🌐 Web UI:      http://localhost:3000"
echo "  🔧 Backend API: http://localhost:8081/api"
if [ -n "$HUB_PID" ]; then
    echo "  🏠 Hub Server:  http://localhost:8080"
fi
echo
echo -e "${BLUE}📋 Service Management:${NC}"
echo "  📊 View logs:   tail -f $LOG_DIR/*.log"
echo "  🛑 Stop all:    ./stop-all.sh"
echo "  🔄 Restart:     ./stop-all.sh && ./start-all.sh"
echo
echo -e "${BLUE}📁 Log files:${NC}"
echo "  Hub Server: $LOG_DIR/hub-server.log"
echo "  Backend:    $LOG_DIR/backend.log"
echo "  Frontend:   $LOG_DIR/frontend.log"
echo

# Save service info
cat > "$LOG_DIR/services.info" << EOF
# GoCryptFS Hub Services
# Started: $(date)

HUB_PID=${HUB_PID:-"N/A"}
BACKEND_PID=$BACKEND_PID
FRONTEND_PID=$FRONTEND_PID

HUB_PORT=8080
BACKEND_PORT=8081
FRONTEND_PORT=3000
EOF

echo -e "${GREEN}✅ Startup complete! All services are running.${NC}"
