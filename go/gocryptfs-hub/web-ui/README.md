# GoCryptFS Hub Web UI

A React TypeScript web interface for the GoCryptFS Hub encryption management system.

## Features

- **User Authentication**: Register and login with secure credential management
- **Vault Management**: Create, list, and manage encrypted vaults
- **Vault Sharing**: Securely share vaults with other users using public key encryption
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Updates**: Automatic refresh and real-time vault status

## Technology Stack

- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Axios** for API communication
- **Lucide React** for icons

## Getting Started

### Prerequisites

- Node.js 16+ and npm
- GoCryptFS Hub server running on `http://localhost:8080`

### Installation

1. Navigate to the web-ui directory:
   ```bash
   cd web-ui
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Configuration

The web UI connects to the GoCryptFS Hub server at `http://localhost:8080` by default. You can change this by modifying the `baseURL` in `src/services/api.ts`.

## Project Structure

```
src/
├── components/
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   └── RegisterForm.tsx
│   ├── vaults/
│   │   ├── VaultList.tsx
│   │   ├── CreateVaultModal.tsx
│   │   └── ShareVaultModal.tsx
│   └── Layout.tsx
├── contexts/
│   └── AuthContext.tsx
├── services/
│   └── api.ts
├── App.tsx
└── index.tsx
```

## API Integration

The web UI integrates with the following GoCryptFS Hub API endpoints:

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/vaults` - List user's vaults
- `POST /api/v1/vaults` - Create new vault
- `GET /api/v1/vaults/{id}/key` - Get vault encryption key
- `POST /api/v1/vaults/{id}/share` - Share vault with user
- `GET /api/v1/users/{username}/key` - Get user's public key

## Security Features

- **Token-based Authentication**: JWT-like tokens stored securely
- **Public Key Encryption**: Vault keys encrypted with user public keys
- **Zero-Knowledge Architecture**: Server never sees unencrypted data
- **Automatic Token Refresh**: Handles authentication state automatically

## Development

### Available Scripts

- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests
- `npm run eject` - Eject from Create React App

### Building for Production

```bash
npm run build
```

The build artifacts will be stored in the `build/` directory.

## Future Enhancements

- File upload/download interface
- Vault mounting status indicators
- User management and permissions
- Audit logs and activity tracking
- Mobile app using React Native
- Offline capability with service workers
