# GoCryptFS Hub Web UI

A React TypeScript web interface for the GoCryptFS Hub encryption system with a secure Node.js backend that executes CLI commands.

## Architecture

```
┌─────────────────┐    HTTP API    ┌─────────────────┐    CLI Commands    ┌─────────────────┐
│   React UI      │ ──────────────► │   Node.js       │ ─────────────────► │   Go CLI        │
│   (Frontend)    │                 │   Backend       │                    │   (Crypto)      │
│   Port 3000     │                 │   Port 8081     │                    │   main binary   │
└─────────────────┘                 └─────────────────┘                    └─────────────────┘
```

- **Frontend**: React TypeScript UI with no cryptographic logic
- **Backend**: Node.js server that executes CLI commands via child processes
- **CLI**: Go application handling all cryptographic operations securely

## Prerequisites

- Node.js (v16 or higher)
- Go (v1.19 or higher)
- npm or yarn

## Quick Start

### 1. Build the Go CLI Application

```bash
cd /home/<USER>/Documents/private/go/gocryptfs-hub
go build -o main .
```

### 2. Install Frontend Dependencies

```bash
cd web-ui
npm install
```

### 3. Install Backend Dependencies

```bash
cd backend
npm install
```

### 4. Start All Services

#### Option A: Start All Services Manually (Recommended for Development)

**Terminal 1 - Backend Server:**
```bash
cd /home/<USER>/Documents/private/go/gocryptfs-hub/web-ui/backend
npm start
```

**Terminal 2 - Frontend Development Server:**
```bash
cd /home/<USER>/Documents/private/go/gocryptfs-hub/web-ui
npm start
```

#### Option B: Start with npm scripts (if you prefer)

From the `web-ui` directory, you can add these scripts to package.json:

```json
{
  "scripts": {
    "start:backend": "cd backend && npm start",
    "start:frontend": "npm start",
    "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\""
  }
}
```

Then install concurrently and run:
```bash
npm install --save-dev concurrently
npm run dev
```

## Access the Application

- **Web UI**: http://localhost:3000
- **Backend API**: http://localhost:8081/api
- **API Documentation**: See backend/README.md

## Available Features

### Authentication
- User registration (creates RSA key pairs via CLI)
- User login with secure authentication

### Vault Management
- Create new encrypted vaults
- List accessible vaults
- Mount vaults to filesystem
- Share vaults with other users

### Security Features
- Zero-knowledge architecture
- All crypto operations in Go CLI
- No sensitive data in frontend
- Secure key management via keymanager package

## Project Structure

```
src/
├── components/
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   └── RegisterForm.tsx
│   ├── vaults/
│   │   ├── VaultList.tsx
│   │   ├── CreateVaultModal.tsx
│   │   └── ShareVaultModal.tsx
│   └── Layout.tsx
├── contexts/
│   └── AuthContext.tsx
├── services/
│   └── api.ts
├── App.tsx
└── index.tsx
```

## API Integration

The web UI integrates with the following GoCryptFS Hub API endpoints:

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/vaults` - List user's vaults
- `POST /api/v1/vaults` - Create new vault
- `GET /api/v1/vaults/{id}/key` - Get vault encryption key
- `POST /api/v1/vaults/{id}/share` - Share vault with user
- `GET /api/v1/users/{username}/key` - Get user's public key

## Security Features

- **Token-based Authentication**: JWT-like tokens stored securely
- **Public Key Encryption**: Vault keys encrypted with user public keys
- **Zero-Knowledge Architecture**: Server never sees unencrypted data
- **Automatic Token Refresh**: Handles authentication state automatically

## Development

### Available Scripts

- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests
- `npm run eject` - Eject from Create React App

### Building for Production

```bash
npm run build
```

The build artifacts will be stored in the `build/` directory.

## Troubleshooting

### Backend Cannot Find CLI Binary
Ensure the Go CLI is built and the path in `backend/server.js` is correct:
```javascript
const GOCRYPTFS_BINARY = path.join(__dirname, '../../main');
```

### Port Conflicts
- Frontend (3000): Change in package.json or set PORT=3001
- Backend (8081): Change PORT in backend/server.js or set PORT=8082

### CORS Issues
The backend includes CORS middleware. If issues persist, check the frontend API base URL in `src/services/api.ts`.

## Project Structure

```
web-ui/
├── backend/                 # Node.js backend server
│   ├── server.js           # Main server file
│   ├── package.json        # Backend dependencies
│   └── README.md           # Backend documentation
├── src/                    # React frontend source
│   ├── components/         # React components
│   ├── services/           # API service layer
│   └── ...
├── package.json            # Frontend dependencies
└── README.md              # This file
```

## Security Notes

- All cryptographic operations are performed by the Go CLI application
- The frontend contains no private keys or sensitive cryptographic logic
- The backend acts as a secure bridge between UI and CLI commands
- User passwords and keys are handled exclusively by the Go keymanager package
