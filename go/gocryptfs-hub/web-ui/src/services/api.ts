import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Types
export interface User {
  id: number;
  username: string;
  public_key: string;
  created_at: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface Vault {
  id: number;
  name: string;
  owner_id: number;
  created_at: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  public_key: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface CreateVaultRequest {
  name: string;
  encrypted_master_key: string;
}

export interface ShareVaultRequest {
  username: string;
  encrypted_master_key: string;
}

export interface VaultKeyResponse {
  encrypted_master_key: string;
}

export interface ApiError {
  error: string;
}

class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor(baseURL: string = 'http://localhost:8080') {
    this.api = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Load token from localStorage
    this.token = localStorage.getItem('auth_token');
    if (this.token) {
      this.setAuthHeader(this.token);
    }

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.clearAuth();
        }
        return Promise.reject(error);
      }
    );
  }

  private setAuthHeader(token: string) {
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  private clearAuth() {
    this.token = null;
    localStorage.removeItem('auth_token');
    delete this.api.defaults.headers.common['Authorization'];
  }

  // Auth methods
  async register(data: RegisterRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/api/v1/auth/register', data);
    this.token = response.data.token;
    localStorage.setItem('auth_token', this.token);
    this.setAuthHeader(this.token);
    return response.data;
  }

  async login(data: LoginRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/api/v1/auth/login', data);
    this.token = response.data.token;
    localStorage.setItem('auth_token', this.token);
    this.setAuthHeader(this.token);
    return response.data;
  }

  logout() {
    this.clearAuth();
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }

  // Vault methods
  async createVault(data: CreateVaultRequest): Promise<Vault> {
    const response: AxiosResponse<Vault> = await this.api.post('/api/v1/vaults', data);
    return response.data;
  }

  async listVaults(): Promise<Vault[]> {
    const response: AxiosResponse<Vault[]> = await this.api.get('/api/v1/vaults');
    return response.data;
  }

  async getVaultKey(vaultId: number): Promise<VaultKeyResponse> {
    const response: AxiosResponse<VaultKeyResponse> = await this.api.get(`/api/v1/vaults/${vaultId}/key`);
    return response.data;
  }

  async shareVault(vaultId: number, data: ShareVaultRequest): Promise<{ message: string }> {
    const response: AxiosResponse<{ message: string }> = await this.api.post(`/api/v1/vaults/${vaultId}/share`, data);
    return response.data;
  }

  // User methods
  async getUserPublicKey(username: string): Promise<{ public_key: string }> {
    const response: AxiosResponse<{ public_key: string }> = await this.api.get(`/api/v1/users/${username}/key`);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; version: string }> {
    const response: AxiosResponse<{ status: string; version: string }> = await this.api.get('/health');
    return response.data;
  }
}

// Create singleton instance
const apiService = new ApiService();
export default apiService;
