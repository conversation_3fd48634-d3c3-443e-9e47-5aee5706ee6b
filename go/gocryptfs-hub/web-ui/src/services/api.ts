import axios from 'axios';

// Backend API base URL
const API_BASE_URL = 'http://localhost:8081/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types
export interface User {
  id: number;
  username: string;
  publicKey: string;
  createdAt: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  output?: string;
  user?: User;
  token?: string;
}

export interface Vault {
  id: number;
  name: string;
  cipherDir?: string;
}

export interface VaultListResponse {
  success: boolean;
  vaults: Vault[];
  output?: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface CreateVaultRequest {
  name: string;
  cipherDir?: string;
}

export interface ShareVaultRequest {
  username: string;
}

export interface MountVaultRequest {
  cipherDir: string;
  mountPoint: string;
}

// API functions
export const authAPI = {
  register: async (data: RegisterRequest): Promise<AuthResponse> => {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  login: async (data: LoginRequest): Promise<AuthResponse> => {
    const response = await api.post('/auth/login', data);
    return response.data;
  },
};

export const vaultAPI = {
  create: async (data: CreateVaultRequest): Promise<AuthResponse> => {
    const response = await api.post('/vaults', data);
    return response.data;
  },

  list: async (): Promise<VaultListResponse> => {
    const response = await api.get('/vaults');
    return response.data;
  },

  mount: async (vaultName: string, data: MountVaultRequest): Promise<AuthResponse> => {
    const response = await api.post(`/vaults/${vaultName}/mount`, data);
    return response.data;
  },

  share: async (vaultName: string, data: ShareVaultRequest): Promise<AuthResponse> => {
    const response = await api.post(`/vaults/${vaultName}/share`, data);
    return response.data;
  },
};

export const systemAPI = {
  getVersion: async (): Promise<{ success: boolean; version: string }> => {
    const response = await api.get('/version');
    return response.data;
  },
};
