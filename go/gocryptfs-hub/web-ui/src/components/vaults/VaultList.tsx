import React, { useState, useEffect } from 'react';
import { Vault } from '../../services/api';
import apiService from '../../services/api';
import { Plus, Folder, Share2, Calendar, User, RefreshCw } from 'lucide-react';
import CreateVaultModal from './CreateVaultModal';
import ShareVaultModal from './ShareVaultModal';

const VaultList: React.FC = () => {
  const [vaults, setVaults] = useState<Vault[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedVault, setSelectedVault] = useState<Vault | null>(null);

  const loadVaults = async () => {
    try {
      setIsLoading(true);
      const vaultList = await apiService.listVaults();
      setVaults(vaultList);
      setError('');
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load vaults');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadVaults();
  }, []);

  const handleCreateVault = () => {
    setShowCreateModal(true);
  };

  const handleShareVault = (vault: Vault) => {
    setSelectedVault(vault);
    setShowShareModal(true);
  };

  const handleVaultCreated = () => {
    setShowCreateModal(false);
    loadVaults();
  };

  const handleVaultShared = () => {
    setShowShareModal(false);
    setSelectedVault(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Vaults</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage your encrypted vaults and share them with other users.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-3">
          <button
            type="button"
            onClick={loadVaults}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button
            type="button"
            onClick={handleCreateVault}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Vault
          </button>
        </div>
      </div>

      {error && (
        <div className="mt-4 rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      <div className="mt-8 flow-root">
        <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            {vaults.length === 0 ? (
              <div className="text-center py-12">
                <Folder className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No vaults</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by creating a new vault.
                </p>
                <div className="mt-6">
                  <button
                    type="button"
                    onClick={handleCreateVault}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Vault
                  </button>
                </div>
              </div>
            ) : (
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                        Name
                      </th>
                      <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Owner
                      </th>
                      <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Created
                      </th>
                      <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {vaults.map((vault) => (
                      <tr key={vault.id} className="hover:bg-gray-50">
                        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                          <div className="flex items-center">
                            <Folder className="h-5 w-5 text-gray-400 mr-3" />
                            <div>
                              <div className="font-medium text-gray-900">{vault.name}</div>
                              <div className="text-gray-500">ID: {vault.id}</div>
                            </div>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <User className="h-4 w-4 text-gray-400 mr-2" />
                            User {vault.owner_id}
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                            {formatDate(vault.created_at)}
                          </div>
                        </td>
                        <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                          <button
                            onClick={() => handleShareVault(vault)}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          >
                            <Share2 className="h-3 w-3 mr-1" />
                            Share
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showCreateModal && (
        <CreateVaultModal
          onClose={() => setShowCreateModal(false)}
          onVaultCreated={handleVaultCreated}
        />
      )}

      {showShareModal && selectedVault && (
        <ShareVaultModal
          vault={selectedVault}
          onClose={() => setShowShareModal(false)}
          onVaultShared={handleVaultShared}
        />
      )}
    </div>
  );
};

export default VaultList;
