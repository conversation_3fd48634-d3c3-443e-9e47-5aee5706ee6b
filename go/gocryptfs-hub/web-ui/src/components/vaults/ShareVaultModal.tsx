import React, { useState } from 'react';
import { X, Share2, User, Key } from 'lucide-react';
import apiService, { Vault } from '../../services/api';

interface ShareVaultModalProps {
  vault: Vault;
  onClose: () => void;
  onVaultShared: () => void;
}

const ShareVaultModal: React.FC<ShareVaultModalProps> = ({ vault, onClose, onVaultShared }) => {
  const [targetUsername, setTargetUsername] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const generateEncryptedKeyForUser = async (username: string): Promise<string> => {
    // In a real implementation, this would:
    // 1. Get the current vault's master key (decrypt with user's private key)
    // 2. Get the target user's public key
    // 3. Encrypt the master key with the target user's public key
    
    // For demo purposes, we'll generate a mock encrypted key
    try {
      await apiService.getUserPublicKey(username);
      const timestamp = Date.now();
      const randomBytes = new Uint8Array(32);
      crypto.getRandomValues(randomBytes);
      const randomHex = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
      return btoa(`encrypted-for-${username}-${timestamp}-${randomHex}`);
    } catch (err) {
      throw new Error('User not found or unable to get public key');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    if (!targetUsername.trim()) {
      setError('Username is required');
      setIsLoading(false);
      return;
    }

    try {
      // Generate encrypted master key for the target user
      const encryptedMasterKey = await generateEncryptedKeyForUser(targetUsername.trim());
      
      await apiService.shareVault(vault.id, {
        username: targetUsername.trim(),
        encrypted_master_key: encryptedMasterKey,
      });

      setSuccess(`Vault "${vault.name}" has been successfully shared with ${targetUsername}`);
      setTimeout(() => {
        onVaultShared();
      }, 2000);
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to share vault');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="sm:flex sm:items-start">
            <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
              <Share2 className="h-6 w-6 text-green-600" />
            </div>
            <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Share Vault
              </h3>
              <div className="mt-2">
                <p className="text-sm text-gray-500">
                  Share vault "<span className="font-medium">{vault.name}</span>" with another user.
                </p>
              </div>

              <form onSubmit={handleSubmit} className="mt-6 space-y-4">
                {error && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="text-sm text-red-700">{error}</div>
                  </div>
                )}

                {success && (
                  <div className="rounded-md bg-green-50 p-4">
                    <div className="text-sm text-green-700">{success}</div>
                  </div>
                )}

                <div>
                  <label htmlFor="targetUsername" className="block text-sm font-medium text-gray-700">
                    Username
                  </label>
                  <div className="mt-1 relative">
                    <input
                      type="text"
                      id="targetUsername"
                      value={targetUsername}
                      onChange={(e) => setTargetUsername(e.target.value)}
                      className="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="Enter username to share with"
                      required
                    />
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Key className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Secure Sharing
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          The vault's master key will be encrypted with the target user's public key.
                          Only they will be able to decrypt and access the vault contents.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    disabled={isLoading || !!success}
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    ) : success ? (
                      'Shared!'
                    ) : (
                      'Share Vault'
                    )}
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShareVaultModal;
