# GoCryptFS Hub Backend

This backend server provides HTTP API endpoints for the React frontend by executing the main GoCryptFS CLI commands.

## Architecture

The backend follows the user's preferred architecture:
- **Frontend**: React TypeScript UI (no crypto logic)
- **Backend**: Node.js server that executes CLI commands
- **CLI**: Go application with all cryptographic operations

## Setup

1. Install dependencies:
```bash
cd backend
npm install
```

2. Build the main Go application:
```bash
cd ../..
go build -o main .
```

3. Start the backend server:
```bash
cd web-ui/backend
npm start
```

The server will run on `http://localhost:8081`

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user

### Vaults
- `POST /api/vaults` - Create a new vault
- `GET /api/vaults` - List all vaults
- `POST /api/vaults/:name/mount` - Mount a vault
- `POST /api/vaults/:name/share` - Share a vault with another user

### System
- `GET /api/version` - Get version information

## How it Works

1. Frontend makes HTTP requests to backend API endpoints
2. Backend executes the corresponding CLI commands using `child_process.spawn()`
3. CLI commands handle all cryptographic operations using the keymanager package
4. Backend parses CLI output and returns JSON responses to frontend

## Security

- All cryptographic operations are handled by the Go CLI application
- Frontend contains no sensitive crypto logic
- Backend acts as a secure bridge between UI and CLI commands
