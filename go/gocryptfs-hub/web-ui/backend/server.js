const express = require('express');
const { spawn } = require('child_process');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8081;

// Middleware
app.use(cors());
app.use(express.json());

// Path to the gocryptfs binary
const GOCRYPTFS_BINARY = path.join(__dirname, '../../main');

// Helper function to execute gocryptfs commands
function executeCommand(command, args = [], input = '') {
  return new Promise((resolve, reject) => {
    const child = spawn(GOCRYPTFS_BINARY, [command, ...args], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    // Send input if provided
    if (input) {
      child.stdin.write(input);
      child.stdin.end();
    }

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ success: true, output: stdout, error: stderr });
      } else {
        reject({ success: false, output: stdout, error: stderr, code });
      }
    });

    child.on('error', (error) => {
      reject({ success: false, error: error.message });
    });
  });
}

// API Routes

// Register a new user
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Execute register command with username and password as input
    const input = `${username}\n${password}\n${password}\n`;
    const result = await executeCommand('register', [], input);
    
    res.json({ 
      success: true, 
      message: 'User registered successfully',
      output: result.output 
    });
  } catch (error) {
    res.status(500).json({ 
      error: error.error || 'Registration failed',
      details: error.output || error.message
    });
  }
});

// Login user
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Execute login command with username and password as input
    const input = `${username}\n${password}\n`;
    const result = await executeCommand('login', [], input);
    
    res.json({ 
      success: true, 
      message: 'Login successful',
      output: result.output 
    });
  } catch (error) {
    res.status(401).json({ 
      error: error.error || 'Login failed',
      details: error.output || error.message
    });
  }
});

// Create a new vault
app.post('/api/vaults', async (req, res) => {
  try {
    const { name, cipherDir } = req.body;
    
    if (!name) {
      return res.status(400).json({ error: 'Vault name is required' });
    }

    // Use default cipher directory if not provided
    const vaultCipherDir = cipherDir || `/tmp/gocryptfs-vaults/${name}`;
    
    const result = await executeCommand('init', [name, vaultCipherDir]);
    
    res.json({ 
      success: true, 
      message: 'Vault created successfully',
      vault: { name, cipherDir: vaultCipherDir },
      output: result.output 
    });
  } catch (error) {
    res.status(500).json({ 
      error: error.error || 'Failed to create vault',
      details: error.output || error.message
    });
  }
});

// List vaults
app.get('/api/vaults', async (req, res) => {
  try {
    const result = await executeCommand('list-vaults');
    
    // Parse the output to extract vault information
    const vaults = [];
    const lines = result.output.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      if (line.includes('Vault:')) {
        const vaultName = line.split('Vault:')[1].trim();
        vaults.push({ name: vaultName, id: vaults.length + 1 });
      }
    }
    
    res.json({ 
      success: true, 
      vaults,
      output: result.output 
    });
  } catch (error) {
    res.status(500).json({ 
      error: error.error || 'Failed to list vaults',
      details: error.output || error.message
    });
  }
});

// Mount a vault
app.post('/api/vaults/:name/mount', async (req, res) => {
  try {
    const { name } = req.params;
    const { cipherDir, mountPoint } = req.body;
    
    if (!cipherDir || !mountPoint) {
      return res.status(400).json({ error: 'Cipher directory and mount point are required' });
    }

    const result = await executeCommand('mount', [name, cipherDir, mountPoint]);
    
    res.json({ 
      success: true, 
      message: 'Vault mounted successfully',
      mountPoint,
      output: result.output 
    });
  } catch (error) {
    res.status(500).json({ 
      error: error.error || 'Failed to mount vault',
      details: error.output || error.message
    });
  }
});

// Share a vault
app.post('/api/vaults/:name/share', async (req, res) => {
  try {
    const { name } = req.params;
    const { username } = req.body;
    
    if (!username) {
      return res.status(400).json({ error: 'Target username is required' });
    }

    // Execute share command with target username as input
    const input = `${username}\n`;
    const result = await executeCommand('share', [name], input);
    
    res.json({ 
      success: true, 
      message: 'Vault shared successfully',
      sharedWith: username,
      output: result.output 
    });
  } catch (error) {
    res.status(500).json({ 
      error: error.error || 'Failed to share vault',
      details: error.output || error.message
    });
  }
});

// Get version information
app.get('/api/version', async (req, res) => {
  try {
    const result = await executeCommand('version');
    
    res.json({ 
      success: true, 
      version: result.output.trim()
    });
  } catch (error) {
    res.status(500).json({ 
      error: error.error || 'Failed to get version',
      details: error.output || error.message
    });
  }
});

// Serve static files from React build
app.use(express.static(path.join(__dirname, '../build')));

// Catch all handler for React Router
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../build/index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`Backend server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api`);
  console.log(`Web UI available at http://localhost:${PORT}`);
});
