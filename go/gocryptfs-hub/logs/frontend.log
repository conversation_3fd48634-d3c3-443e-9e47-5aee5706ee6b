
> web-ui@0.1.0 start
> react-scripts start

(node:22207) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:22207) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
Starting the development server...

Failed to compile.

Attempted import error: '../services/api' does not contain a default export (imported as 'apiService').
WARNING in [eslint] 
src/components/Layout.tsx
  Line 3:32:  'Key' is defined but never used  @typescript-eslint/no-unused-vars

src/components/vaults/VaultList.tsx
  Line 55:9:  'formatDate' is assigned a value but never used  @typescript-eslint/no-unused-vars

ERROR in ./src/contexts/AuthContext.tsx 29:12-38
export 'default' (imported as 'apiService') was not found in '../services/api' (possible exports: authAPI, systemAPI, vaultAPI)

ERROR in ./src/contexts/AuthContext.tsx 46:29-45
export 'default' (imported as 'apiService') was not found in '../services/api' (possible exports: authAPI, systemAPI, vaultAPI)

ERROR in ./src/contexts/AuthContext.tsx 54:29-48
export 'default' (imported as 'apiService') was not found in '../services/api' (possible exports: authAPI, systemAPI, vaultAPI)

ERROR in ./src/contexts/AuthContext.tsx 61:4-21
export 'default' (imported as 'apiService') was not found in '../services/api' (possible exports: authAPI, systemAPI, vaultAPI)

ERROR in ./src/contexts/AuthContext.tsx 66:31-57
export 'default' (imported as 'apiService') was not found in '../services/api' (possible exports: authAPI, systemAPI, vaultAPI)

ERROR in ./src/index.css (./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[5].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[5].use[2]!./node_modules/source-map-loader/dist/cjs.js!./src/index.css)
Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):
Error: It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.
    at We (/home/<USER>/Documents/private/go/gocryptfs-hub/web-ui/node_modules/tailwindcss/dist/lib.js:35:2121)
    at LazyResult.runOnRoot (/home/<USER>/Documents/private/go/gocryptfs-hub/web-ui/node_modules/postcss/lib/lazy-result.js:361:16)
    at LazyResult.runAsync (/home/<USER>/Documents/private/go/gocryptfs-hub/web-ui/node_modules/postcss/lib/lazy-result.js:290:26)
    at LazyResult.async (/home/<USER>/Documents/private/go/gocryptfs-hub/web-ui/node_modules/postcss/lib/lazy-result.js:192:30)
    at LazyResult.then (/home/<USER>/Documents/private/go/gocryptfs-hub/web-ui/node_modules/postcss/lib/lazy-result.js:436:17)

webpack compiled with 6 errors and 1 warning
ERROR in src/contexts/AuthContext.tsx:2:8
TS1192: Module '"/home/<USER>/Documents/private/go/gocryptfs-hub/web-ui/src/services/api"' has no default export.
    1 | import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
  > 2 | import apiService, { User, LoginRequest, RegisterRequest } from '../services/api';
      |        ^^^^^^^^^^
    3 |
    4 | interface AuthContextType {
    5 |   user: User | null;

