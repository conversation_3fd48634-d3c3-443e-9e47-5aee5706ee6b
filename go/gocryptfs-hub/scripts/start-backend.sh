#!/bin/bash

# Start Backend API Only
# This script starts just the Node.js Backend API

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/web-ui/backend"
LOG_DIR="$PROJECT_ROOT/logs"

echo -e "${BLUE}🔧 Starting Backend API${NC}"

# Create log directory
mkdir -p "$LOG_DIR"

# Check if Node.js is installed
if ! command -v node >/dev/null 2>&1; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    exit 1
fi

if ! command -v npm >/dev/null 2>&1; then
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
fi

# Check if backend directory exists
if [ ! -d "$BACKEND_DIR" ]; then
    echo -e "${RED}❌ Backend directory not found: $BACKEND_DIR${NC}"
    exit 1
fi

# Install dependencies if needed
cd "$BACKEND_DIR"
if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing backend dependencies...${NC}"
    npm install
    echo -e "${GREEN}✅ Backend dependencies installed${NC}"
fi

# Check if port is available
if nc -z localhost 8081 2>/dev/null; then
    echo -e "${RED}❌ Port 8081 is already in use${NC}"
    exit 1
fi

# Check if Go CLI binary exists
if [ ! -f "$PROJECT_ROOT/main" ]; then
    echo -e "${YELLOW}⚠️  Go CLI binary not found. Building...${NC}"
    cd "$PROJECT_ROOT"
    go build -o main .
    echo -e "${GREEN}✅ Go CLI built successfully${NC}"
    cd "$BACKEND_DIR"
fi

# Start Backend API
echo -e "${YELLOW}🚀 Starting Backend API on port 8081...${NC}"

if [ "$1" = "--foreground" ] || [ "$1" = "-f" ]; then
    # Run in foreground
    echo -e "${BLUE}Running in foreground mode (Ctrl+C to stop)${NC}"
    npm start
else
    # Run in background
    nohup npm start > "$LOG_DIR/backend.log" 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > "$LOG_DIR/backend.pid"
    
    echo "Backend API PID: $BACKEND_PID"
    echo -e "${GREEN}✅ Backend API started successfully${NC}"
    echo
    echo -e "${BLUE}📍 Access Point:${NC}"
    echo "  🔧 Backend API: http://localhost:8081/api"
    echo
    echo -e "${BLUE}📋 Management:${NC}"
    echo "  📊 View logs: tail -f $LOG_DIR/backend.log"
    echo "  🛑 Stop:      kill $BACKEND_PID"
    echo "  🛑 Stop all:  ../stop-all.sh"
fi
